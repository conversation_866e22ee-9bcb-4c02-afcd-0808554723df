import 'package:json_annotation/json_annotation.dart';

part 'trade.g.dart';

enum TradeType { buy, sell }
enum TradeStatus { open, closed, cancelled }

@JsonSerializable()
class Trade {
  final String id;
  final String coinId;
  final String coinSymbol;
  final String coinName;
  final TradeType type;
  final double amount;
  final double entryPrice;
  final double? exitPrice;
  final double? stopLoss;
  final double? takeProfit;
  final TradeStatus status;
  final DateTime createdAt;
  final DateTime? closedAt;
  final String? notes;

  Trade({
    required this.id,
    required this.coinId,
    required this.coinSymbol,
    required this.coinName,
    required this.type,
    required this.amount,
    required this.entryPrice,
    this.exitPrice,
    this.stopLoss,
    this.takeProfit,
    required this.status,
    required this.createdAt,
    this.closedAt,
    this.notes,
  });

  factory Trade.fromJson(Map<String, dynamic> json) => _$TradeFromJson(json);
  Map<String, dynamic> toJson() => _$TradeToJson(this);

  double get totalValue => amount * entryPrice;
  
  double? get currentValue {
    if (exitPrice != null) {
      return amount * exitPrice!;
    }
    return null;
  }
  
  double? get profitLoss {
    if (exitPrice != null) {
      if (type == TradeType.buy) {
        return (exitPrice! - entryPrice) * amount;
      } else {
        return (entryPrice - exitPrice!) * amount;
      }
    }
    return null;
  }
  
  double? get profitLossPercentage {
    final pl = profitLoss;
    if (pl != null) {
      return (pl / totalValue) * 100;
    }
    return null;
  }
  
  bool get isProfit => (profitLoss ?? 0) > 0;
  
  String get formattedProfitLoss {
    final pl = profitLoss;
    if (pl == null) return '--';
    return '${pl >= 0 ? '+' : ''}\$${pl.toStringAsFixed(2)}';
  }
  
  String get formattedProfitLossPercentage {
    final plp = profitLossPercentage;
    if (plp == null) return '--';
    return '${plp >= 0 ? '+' : ''}${plp.toStringAsFixed(2)}%';
  }

  Trade copyWith({
    String? id,
    String? coinId,
    String? coinSymbol,
    String? coinName,
    TradeType? type,
    double? amount,
    double? entryPrice,
    double? exitPrice,
    double? stopLoss,
    double? takeProfit,
    TradeStatus? status,
    DateTime? createdAt,
    DateTime? closedAt,
    String? notes,
  }) {
    return Trade(
      id: id ?? this.id,
      coinId: coinId ?? this.coinId,
      coinSymbol: coinSymbol ?? this.coinSymbol,
      coinName: coinName ?? this.coinName,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      entryPrice: entryPrice ?? this.entryPrice,
      exitPrice: exitPrice ?? this.exitPrice,
      stopLoss: stopLoss ?? this.stopLoss,
      takeProfit: takeProfit ?? this.takeProfit,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      closedAt: closedAt ?? this.closedAt,
      notes: notes ?? this.notes,
    );
  }
}
