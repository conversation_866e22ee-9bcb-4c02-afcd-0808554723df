import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/coins_provider.dart';
import '../models/coin.dart';
import 'trade_screen.dart';

class CoinsScreen extends StatefulWidget {
  const CoinsScreen({super.key});

  @override
  State<CoinsScreen> createState() => _CoinsScreenState();
}

class _CoinsScreenState extends State<CoinsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العملات الرقمية'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<CoinsProvider>().refreshCoins();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'البحث عن عملة...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // قائمة العملات
          Expanded(
            child: Consumer<CoinsProvider>(
              builder: (context, coinsProvider, child) {
                if (coinsProvider.isLoading && coinsProvider.coins.isEmpty) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (coinsProvider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'خطأ في تحميل البيانات',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          coinsProvider.error!,
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            coinsProvider.loadCoins();
                          },
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                final coins = _searchQuery.isEmpty
                    ? coinsProvider.coins
                    : coinsProvider.searchCoins(_searchQuery);

                if (coins.isEmpty) {
                  return const Center(
                    child: Text('لا توجد عملات متاحة'),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => coinsProvider.refreshCoins(),
                  child: ListView.builder(
                    itemCount: coins.length,
                    itemBuilder: (context, index) {
                      final coin = coins[index];
                      return CoinListTile(coin: coin);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class CoinListTile extends StatelessWidget {
  final Coin coin;

  const CoinListTile({super.key, required this.coin});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundImage: NetworkImage(coin.image),
          onBackgroundImageError: (_, __) {},
          child: coin.image.isEmpty
              ? Text(coin.symbol.substring(0, 2).toUpperCase())
              : null,
        ),
        title: Text(
          coin.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(coin.symbol.toUpperCase()),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              coin.formattedPrice,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: coin.isPriceUp ? Colors.green : Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                coin.formattedPriceChange,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TradeScreen(coin: coin),
            ),
          );
        },
      ),
    );
  }
}
