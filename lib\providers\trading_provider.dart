import 'dart:async';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/trade.dart';
import '../models/portfolio.dart';
import '../services/storage_service.dart';
import 'coins_provider.dart';

class TradingProvider with ChangeNotifier {
  Portfolio _portfolio = Portfolio.initial();
  final CoinsProvider _coinsProvider;
  Timer? _monitoringTimer;

  Portfolio get portfolio => _portfolio;
  List<Trade> get openTrades => _portfolio.openTrades;
  List<Trade> get closedTrades => _portfolio.closedTrades;

  TradingProvider(this._coinsProvider) {
    _loadPortfolio();
    _startTradeMonitoring();
  }

  @override
  void dispose() {
    _monitoringTimer?.cancel();
    super.dispose();
  }

  void _loadPortfolio() {
    final savedPortfolio = StorageService.getPortfolio();
    if (savedPortfolio != null) {
      _portfolio = savedPortfolio;
    } else {
      _portfolio = Portfolio.initial();
      _savePortfolio();
    }
    notifyListeners();
  }

  Future<void> _savePortfolio() async {
    await StorageService.savePortfolio(_portfolio);
  }

  void _startTradeMonitoring() {
    _monitoringTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _checkStopLossAndTakeProfit();
    });
  }

  Future<bool> placeTrade({
    required String coinId,
    required TradeType type,
    required double amount,
    required double price,
    double? stopLoss,
    double? takeProfit,
    String? notes,
  }) async {
    final coin = _coinsProvider.findCoinById(coinId);
    if (coin == null) return false;

    final totalCost = amount * price;

    // التحقق من الرصيد المتاح
    if (totalCost > _portfolio.currentBalance) {
      return false;
    }

    final trade = Trade(
      id: const Uuid().v4(),
      coinId: coinId,
      coinSymbol: coin.symbol,
      coinName: coin.name,
      type: type,
      amount: amount,
      entryPrice: price,
      stopLoss: stopLoss,
      takeProfit: takeProfit,
      status: TradeStatus.open,
      createdAt: DateTime.now(),
      notes: notes,
    );

    // تحديث الرصيد
    final newBalance = _portfolio.currentBalance - totalCost;
    final updatedTrades = [..._portfolio.trades, trade];

    _portfolio = _portfolio.copyWith(
      currentBalance: newBalance,
      trades: updatedTrades,
      lastUpdated: DateTime.now(),
    );

    await _savePortfolio();
    await StorageService.saveTrade(trade);
    notifyListeners();

    return true;
  }

  Future<bool> closeTrade(String tradeId, double exitPrice) async {
    final tradeIndex = _portfolio.trades.indexWhere((t) => t.id == tradeId);
    if (tradeIndex == -1) return false;

    final trade = _portfolio.trades[tradeIndex];
    if (trade.status != TradeStatus.open) return false;

    final closedTrade = trade.copyWith(
      exitPrice: exitPrice,
      status: TradeStatus.closed,
      closedAt: DateTime.now(),
    );

    // حساب الربح/الخسارة وتحديث الرصيد
    final returnedAmount = trade.amount * exitPrice;
    final newBalance = _portfolio.currentBalance + returnedAmount;

    final updatedTrades = [..._portfolio.trades];
    updatedTrades[tradeIndex] = closedTrade;

    _portfolio = _portfolio.copyWith(
      currentBalance: newBalance,
      trades: updatedTrades,
      lastUpdated: DateTime.now(),
    );

    await _savePortfolio();
    await StorageService.saveTrade(closedTrade);
    notifyListeners();

    return true;
  }

  Future<bool> cancelTrade(String tradeId) async {
    final tradeIndex = _portfolio.trades.indexWhere((t) => t.id == tradeId);
    if (tradeIndex == -1) return false;

    final trade = _portfolio.trades[tradeIndex];
    if (trade.status != TradeStatus.open) return false;

    final cancelledTrade = trade.copyWith(
      status: TradeStatus.cancelled,
      closedAt: DateTime.now(),
    );

    // إرجاع المبلغ إلى الرصيد
    final returnedAmount = trade.totalValue;
    final newBalance = _portfolio.currentBalance + returnedAmount;

    final updatedTrades = [..._portfolio.trades];
    updatedTrades[tradeIndex] = cancelledTrade;

    _portfolio = _portfolio.copyWith(
      currentBalance: newBalance,
      trades: updatedTrades,
      lastUpdated: DateTime.now(),
    );

    await _savePortfolio();
    await StorageService.saveTrade(cancelledTrade);
    notifyListeners();

    return true;
  }

  void _checkStopLossAndTakeProfit() {
    for (final trade in openTrades) {
      final coin = _coinsProvider.findCoinById(trade.coinId);
      if (coin == null) continue;

      final currentPrice = coin.currentPrice;
      bool shouldClose = false;

      if (trade.type == TradeType.buy) {
        // للشراء: إغلاق عند وقف الخسارة أو أخذ الربح
        if (trade.stopLoss != null && currentPrice <= trade.stopLoss!) {
          shouldClose = true;
        } else if (trade.takeProfit != null &&
            currentPrice >= trade.takeProfit!) {
          shouldClose = true;
        }
      } else {
        // للبيع: إغلاق عند وقف الخسارة أو أخذ الربح
        if (trade.stopLoss != null && currentPrice >= trade.stopLoss!) {
          shouldClose = true;
        } else if (trade.takeProfit != null &&
            currentPrice <= trade.takeProfit!) {
          shouldClose = true;
        }
      }

      if (shouldClose) {
        closeTrade(trade.id, currentPrice);
      }
    }
  }

  Future<void> resetPortfolio({double initialBalance = 10000.0}) async {
    await StorageService.clearPortfolio();
    _portfolio = Portfolio.initial(balance: initialBalance);
    await _savePortfolio();
    notifyListeners();
  }

  double getCurrentTradeValue(Trade trade) {
    final coin = _coinsProvider.findCoinById(trade.coinId);
    if (coin == null) return trade.totalValue;

    return trade.amount * coin.currentPrice;
  }

  double getCurrentTradeProfitLoss(Trade trade) {
    final coin = _coinsProvider.findCoinById(trade.coinId);
    if (coin == null) return 0;

    final currentPrice = coin.currentPrice;
    if (trade.type == TradeType.buy) {
      return (currentPrice - trade.entryPrice) * trade.amount;
    } else {
      return (trade.entryPrice - currentPrice) * trade.amount;
    }
  }
}
