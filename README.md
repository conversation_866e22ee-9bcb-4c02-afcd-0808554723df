# تطبيق مضاربة العملات الرقمية - Solana Meme Trader

تطبيق Flutter لمضاربة العملات الرقمية على شبكة Solana للـ memecoins بشكل تجريبي (Paper Trading).

## الميزات الرئيسية

### 🚀 المضاربة التجريبية
- مضاربة وهمية بأموال افتراضية
- أسعار حقيقية من CoinGecko API
- تنفيذ صفقات الشراء والبيع
- رصيد ابتدائي قابل للتخصيص (افتراضي: $10,000)

### 📊 إدارة المخاطر
- **وقف الخسارة (Stop Loss)**: إغلاق تلقائي عند الوصول لسعر محدد
- **أخذ الربح (Take Profit)**: إغلاق تلقائي عند تحقيق الربح المستهدف
- مراقبة مستمرة للصفقات المفتوحة

### 💰 العملات المدعومة
العملات الرقمية الشائعة على شبكة Solana:
- BONK
- WIF (dogwifcoin)
- BOME (Book of Meme)
- MEW (Cat in a Dogs World)
- BODEN (Jeo Boden)
- SLERF
- WEN
- MYRO
- POPCAT
- MOG
- PEPE
- SHIB
- FLOKI
- BABYDOGE
- SAMO

### 📈 التقارير والإحصائيات
- ملخص المحفظة الشامل
- تتبع الربح/الخسارة الإجمالي
- معدل نجاح الصفقات
- تاريخ كامل للصفقات
- إحصائيات مفصلة لكل صفقة

## هيكل التطبيق

### الشاشات الرئيسية
1. **شاشة العملات**: عرض قائمة العملات مع الأسعار الحية
2. **شاشة المحفظة**: ملخص الرصيد والصفقات المفتوحة
3. **شاشة الصفقات**: تاريخ جميع الصفقات (مفتوحة/مغلقة)
4. **شاشة الإعدادات**: إدارة المحفظة والإعدادات

### المكونات التقنية
- **النماذج (Models)**: Coin, Trade, Portfolio
- **الخدمات (Services)**: CoinGecko API, التخزين المحلي
- **مزودي الحالة (Providers)**: إدارة العملات والتداول
- **واجهة المستخدم**: Material Design مع دعم اللغة العربية

## التقنيات المستخدمة

### Flutter Packages
- `provider`: إدارة الحالة
- `http`: طلبات API
- `shared_preferences`: التخزين المحلي
- `fl_chart`: الرسوم البيانية
- `intl`: تنسيق التواريخ والأرقام
- `json_annotation`: تسلسل JSON
- `uuid`: توليد معرفات فريدة

### APIs المستخدمة
- **CoinGecko API**: للحصول على أسعار العملات الحقيقية
- **التحديث التلقائي**: كل 30 ثانية للأسعار
- **مراقبة الصفقات**: كل 10 ثواني لوقف الخسارة/أخذ الربح

## كيفية الاستخدام

### 1. عرض العملات
- تصفح قائمة العملات المدعومة
- البحث عن عملة معينة
- عرض السعر الحالي والتغيير خلال 24 ساعة

### 2. تنفيذ صفقة
- اختيار نوع الصفقة (شراء/بيع)
- تحديد الكمية المراد تداولها
- تعيين وقف الخسارة (اختياري)
- تعيين أخذ الربح (اختياري)
- إضافة ملاحظات (اختياري)

### 3. إدارة الصفقات
- مراقبة الصفقات المفتوحة في الوقت الفعلي
- إغلاق الصفقات يدوياً
- إلغاء الصفقات واسترداد الرصيد
- عرض تاريخ جميع الصفقات

### 4. تتبع الأداء
- مراجعة إحصائيات المحفظة
- تحليل معدل النجاح
- تصدير البيانات للمراجعة
- إعادة تعيين المحفظة عند الحاجة

## الأمان والخصوصية

- **لا توجد أموال حقيقية**: جميع الصفقات وهمية
- **التخزين المحلي**: البيانات محفوظة على الجهاز فقط
- **عدم جمع البيانات**: لا يتم إرسال بيانات شخصية
- **مفتوح المصدر**: الكود متاح للمراجعة

## إخلاء المسؤولية

⚠️ **تنبيه مهم**: هذا تطبيق تعليمي للتدريب على المضاربة فقط. لا يتم استخدام أموال حقيقية. الأسعار المعروضة حقيقية ولكن الصفقات وهمية. يرجى استشارة خبير مالي قبل الاستثمار الحقيقي.

## المتطلبات

- Flutter SDK 3.10.0 أو أحدث
- Dart SDK 3.0.0 أو أحدث
- اتصال بالإنترنت لجلب أسعار العملات

## التطوير المستقبلي

- إضافة رسوم بيانية للأسعار
- دعم المزيد من العملات
- تحليلات تقنية متقدمة
- تنبيهات الأسعار
- مشاركة الإحصائيات
- دعم الوضع المظلم

---

تم تطوير هذا التطبيق لأغراض التعلم والتدريب على مضاربة العملات الرقمية بطريقة آمنة وبدون مخاطر مالية.
