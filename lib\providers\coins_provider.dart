import 'dart:async';
import 'package:flutter/material.dart';
import '../models/coin.dart';
import '../services/coingecko_service.dart';

class CoinsProvider with ChangeNotifier {
  final CoinGeckoService _coinGeckoService = CoinGeckoService();
  
  List<Coin> _coins = [];
  bool _isLoading = false;
  String? _error;
  Timer? _refreshTimer;
  
  List<Coin> get coins => _coins;
  bool get isLoading => _isLoading;
  String? get error => _error;

  CoinsProvider() {
    loadCoins();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      refreshCoins();
    });
  }

  Future<void> loadCoins() async {
    _setLoading(true);
    _setError(null);
    
    try {
      final coins = await _coinGeckoService.getSolanaCoins();
      _coins = coins;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshCoins() async {
    try {
      final coins = await _coinGeckoService.getSolanaCoins();
      _coins = coins;
      _setError(null);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<Coin?> getCoinById(String coinId) async {
    try {
      return await _coinGeckoService.getCoinById(coinId);
    } catch (e) {
      _setError(e.toString());
      return null;
    }
  }

  Future<List<double>> getCoinPriceHistory(String coinId, int days) async {
    try {
      return await _coinGeckoService.getCoinPriceHistory(coinId, days);
    } catch (e) {
      _setError(e.toString());
      return [];
    }
  }

  Coin? findCoinById(String coinId) {
    try {
      return _coins.firstWhere((coin) => coin.id == coinId);
    } catch (e) {
      return null;
    }
  }

  List<Coin> searchCoins(String query) {
    if (query.isEmpty) return _coins;
    
    final lowercaseQuery = query.toLowerCase();
    return _coins.where((coin) {
      return coin.name.toLowerCase().contains(lowercaseQuery) ||
             coin.symbol.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  List<Coin> get topGainers {
    final gainers = _coins.where((coin) => (coin.priceChangePercentage24h ?? 0) > 0).toList();
    gainers.sort((a, b) => (b.priceChangePercentage24h ?? 0).compareTo(a.priceChangePercentage24h ?? 0));
    return gainers.take(10).toList();
  }

  List<Coin> get topLosers {
    final losers = _coins.where((coin) => (coin.priceChangePercentage24h ?? 0) < 0).toList();
    losers.sort((a, b) => (a.priceChangePercentage24h ?? 0).compareTo(b.priceChangePercentage24h ?? 0));
    return losers.take(10).toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
