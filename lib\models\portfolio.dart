import 'package:json_annotation/json_annotation.dart';
import 'trade.dart';

part 'portfolio.g.dart';

@JsonSerializable()
class Portfolio {
  final double initialBalance;
  final double currentBalance;
  final List<Trade> trades;
  final DateTime createdAt;
  final DateTime lastUpdated;

  Portfolio({
    required this.initialBalance,
    required this.currentBalance,
    required this.trades,
    required this.createdAt,
    required this.lastUpdated,
  });

  factory Portfolio.fromJson(Map<String, dynamic> json) => _$PortfolioFromJson(json);
  Map<String, dynamic> toJson() => _$PortfolioToJson(this);

  factory Portfolio.initial({double balance = 10000.0}) {
    final now = DateTime.now();
    return Portfolio(
      initialBalance: balance,
      currentBalance: balance,
      trades: [],
      createdAt: now,
      lastUpdated: now,
    );
  }

  List<Trade> get openTrades => trades.where((t) => t.status == TradeStatus.open).toList();
  List<Trade> get closedTrades => trades.where((t) => t.status == TradeStatus.closed).toList();
  
  double get totalProfitLoss {
    return closedTrades.fold(0.0, (sum, trade) => sum + (trade.profitLoss ?? 0));
  }
  
  double get totalProfitLossPercentage {
    if (initialBalance == 0) return 0;
    return (totalProfitLoss / initialBalance) * 100;
  }
  
  int get totalTrades => trades.length;
  int get winningTrades => closedTrades.where((t) => t.isProfit).length;
  int get losingTrades => closedTrades.where((t) => !t.isProfit).length;
  
  double get winRate {
    if (closedTrades.isEmpty) return 0;
    return (winningTrades / closedTrades.length) * 100;
  }
  
  String get formattedBalance => '\$${currentBalance.toStringAsFixed(2)}';
  String get formattedProfitLoss => '${totalProfitLoss >= 0 ? '+' : ''}\$${totalProfitLoss.toStringAsFixed(2)}';
  String get formattedProfitLossPercentage => '${totalProfitLossPercentage >= 0 ? '+' : ''}${totalProfitLossPercentage.toStringAsFixed(2)}%';

  Portfolio copyWith({
    double? initialBalance,
    double? currentBalance,
    List<Trade>? trades,
    DateTime? createdAt,
    DateTime? lastUpdated,
  }) {
    return Portfolio(
      initialBalance: initialBalance ?? this.initialBalance,
      currentBalance: currentBalance ?? this.currentBalance,
      trades: trades ?? this.trades,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
