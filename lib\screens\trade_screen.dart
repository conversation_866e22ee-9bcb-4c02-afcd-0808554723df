import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/coin.dart';
import '../models/trade.dart';
import '../providers/trading_provider.dart';

class TradeScreen extends StatefulWidget {
  final Coin coin;

  const TradeScreen({super.key, required this.coin});

  @override
  State<TradeScreen> createState() => _TradeScreenState();
}

class _TradeScreenState extends State<TradeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _stopLossController = TextEditingController();
  final _takeProfitController = TextEditingController();
  final _notesController = TextEditingController();
  
  TradeType _tradeType = TradeType.buy;
  bool _useStopLoss = false;
  bool _useTakeProfit = false;

  @override
  void dispose() {
    _amountController.dispose();
    _stopLossController.dispose();
    _takeProfitController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تداول ${widget.coin.symbol.toUpperCase()}'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات العملة
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundImage: NetworkImage(widget.coin.image),
                            onBackgroundImageError: (_, __) {},
                            child: widget.coin.image.isEmpty 
                                ? Text(widget.coin.symbol.substring(0, 2).toUpperCase())
                                : null,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.coin.name,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  widget.coin.symbol.toUpperCase(),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                widget.coin.formattedPrice,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: widget.coin.isPriceUp ? Colors.green : Colors.red,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  widget.coin.formattedPriceChange,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // نوع الصفقة
              const Text(
                'نوع الصفقة',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<TradeType>(
                      title: const Text('شراء'),
                      value: TradeType.buy,
                      groupValue: _tradeType,
                      onChanged: (value) {
                        setState(() {
                          _tradeType = value!;
                        });
                      },
                      activeColor: Colors.green,
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<TradeType>(
                      title: const Text('بيع'),
                      value: TradeType.sell,
                      groupValue: _tradeType,
                      onChanged: (value) {
                        setState(() {
                          _tradeType = value!;
                        });
                      },
                      activeColor: Colors.red,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // الكمية
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'الكمية',
                  hintText: 'أدخل الكمية المراد تداولها',
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال الكمية';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'يرجى إدخال كمية صحيحة';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // وقف الخسارة
              CheckboxListTile(
                title: const Text('استخدام وقف الخسارة'),
                value: _useStopLoss,
                onChanged: (value) {
                  setState(() {
                    _useStopLoss = value!;
                    if (!_useStopLoss) {
                      _stopLossController.clear();
                    }
                  });
                },
              ),
              
              if (_useStopLoss)
                TextFormField(
                  controller: _stopLossController,
                  decoration: const InputDecoration(
                    labelText: 'سعر وقف الخسارة',
                    hintText: 'أدخل سعر وقف الخسارة',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (_useStopLoss && (value == null || value.isEmpty)) {
                      return 'يرجى إدخال سعر وقف الخسارة';
                    }
                    if (_useStopLoss) {
                      final price = double.tryParse(value!);
                      if (price == null || price <= 0) {
                        return 'يرجى إدخال سعر صحيح';
                      }
                    }
                    return null;
                  },
                ),
              
              const SizedBox(height: 16),
              
              // أخذ الربح
              CheckboxListTile(
                title: const Text('استخدام أخذ الربح'),
                value: _useTakeProfit,
                onChanged: (value) {
                  setState(() {
                    _useTakeProfit = value!;
                    if (!_useTakeProfit) {
                      _takeProfitController.clear();
                    }
                  });
                },
              ),
              
              if (_useTakeProfit)
                TextFormField(
                  controller: _takeProfitController,
                  decoration: const InputDecoration(
                    labelText: 'سعر أخذ الربح',
                    hintText: 'أدخل سعر أخذ الربح',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (_useTakeProfit && (value == null || value.isEmpty)) {
                      return 'يرجى إدخال سعر أخذ الربح';
                    }
                    if (_useTakeProfit) {
                      final price = double.tryParse(value!);
                      if (price == null || price <= 0) {
                        return 'يرجى إدخال سعر صحيح';
                      }
                    }
                    return null;
                  },
                ),
              
              const SizedBox(height: 16),
              
              // ملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  hintText: 'أضف ملاحظات حول الصفقة',
                ),
                maxLines: 3,
              ),
              
              const SizedBox(height: 24),
              
              // زر التنفيذ
              SizedBox(
                width: double.infinity,
                child: Consumer<TradingProvider>(
                  builder: (context, tradingProvider, child) {
                    return ElevatedButton(
                      onPressed: () => _executeTrade(context, tradingProvider),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _tradeType == TradeType.buy ? Colors.green : Colors.red,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(
                        _tradeType == TradeType.buy ? 'تنفيذ الشراء' : 'تنفيذ البيع',
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _executeTrade(BuildContext context, TradingProvider tradingProvider) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.parse(_amountController.text);
    final stopLoss = _useStopLoss ? double.tryParse(_stopLossController.text) : null;
    final takeProfit = _useTakeProfit ? double.tryParse(_takeProfitController.text) : null;
    final notes = _notesController.text.isNotEmpty ? _notesController.text : null;

    final success = await tradingProvider.placeTrade(
      coinId: widget.coin.id,
      type: _tradeType,
      amount: amount,
      price: widget.coin.currentPrice,
      stopLoss: stopLoss,
      takeProfit: takeProfit,
      notes: notes,
    );

    if (success) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تنفيذ الصفقة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } else {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تنفيذ الصفقة - رصيد غير كافي'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
