import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/portfolio.dart';
import '../models/trade.dart';

class StorageService {
  static const String _portfolioKey = 'portfolio';
  static const String _tradesKey = 'trades';
  static const String _settingsKey = 'settings';

  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call init() first.');
    }
    return _prefs!;
  }

  // Portfolio Management
  static Future<void> savePortfolio(Portfolio portfolio) async {
    final portfolioJson = json.encode(portfolio.toJson());
    await prefs.setString(_portfolioKey, portfolioJson);
  }

  static Portfolio? getPortfolio() {
    final portfolioJson = prefs.getString(_portfolioKey);
    if (portfolioJson != null) {
      try {
        final portfolioData = json.decode(portfolioJson);
        return Portfolio.fromJson(portfolioData);
      } catch (e) {
        print('خطأ في قراءة المحفظة: $e');
        return null;
      }
    }
    return null;
  }

  static Future<void> clearPortfolio() async {
    await prefs.remove(_portfolioKey);
    await prefs.remove(_tradesKey);
  }

  // Trades Management
  static Future<void> saveTrade(Trade trade) async {
    final trades = getTrades();
    final existingIndex = trades.indexWhere((t) => t.id == trade.id);
    
    if (existingIndex != -1) {
      trades[existingIndex] = trade;
    } else {
      trades.add(trade);
    }
    
    await _saveTrades(trades);
  }

  static Future<void> deleteTrade(String tradeId) async {
    final trades = getTrades();
    trades.removeWhere((t) => t.id == tradeId);
    await _saveTrades(trades);
  }

  static List<Trade> getTrades() {
    final tradesJson = prefs.getString(_tradesKey);
    if (tradesJson != null) {
      try {
        final List<dynamic> tradesData = json.decode(tradesJson);
        return tradesData.map((data) => Trade.fromJson(data)).toList();
      } catch (e) {
        print('خطأ في قراءة الصفقات: $e');
        return [];
      }
    }
    return [];
  }

  static Future<void> _saveTrades(List<Trade> trades) async {
    final tradesJson = json.encode(trades.map((t) => t.toJson()).toList());
    await prefs.setString(_tradesKey, tradesJson);
  }

  // Settings Management
  static Future<void> saveSetting(String key, dynamic value) async {
    final settings = getSettings();
    settings[key] = value;
    await _saveSettings(settings);
  }

  static T? getSetting<T>(String key, {T? defaultValue}) {
    final settings = getSettings();
    return settings[key] as T? ?? defaultValue;
  }

  static Map<String, dynamic> getSettings() {
    final settingsJson = prefs.getString(_settingsKey);
    if (settingsJson != null) {
      try {
        return json.decode(settingsJson);
      } catch (e) {
        print('خطأ في قراءة الإعدادات: $e');
        return {};
      }
    }
    return {};
  }

  static Future<void> _saveSettings(Map<String, dynamic> settings) async {
    final settingsJson = json.encode(settings);
    await prefs.setString(_settingsKey, settingsJson);
  }

  // Utility Methods
  static Future<void> clearAllData() async {
    await prefs.clear();
  }

  static bool hasPortfolio() {
    return prefs.containsKey(_portfolioKey);
  }

  static Future<void> exportData() async {
    // يمكن تطوير هذه الوظيفة لاحقاً لتصدير البيانات
    final portfolio = getPortfolio();
    final trades = getTrades();
    final settings = getSettings();
    
    final exportData = {
      'portfolio': portfolio?.toJson(),
      'trades': trades.map((t) => t.toJson()).toList(),
      'settings': settings,
      'exportDate': DateTime.now().toIso8601String(),
    };
    
    print('بيانات التصدير: ${json.encode(exportData)}');
  }
}
