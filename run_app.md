# تشغيل تطبيق مضاربة العملات الرقمية

## المتطلبات المسبقة

1. **تثبيت Flutter SDK**:
   - قم بتحميل Flutter من: https://flutter.dev/docs/get-started/install
   - أضف Flutter إلى PATH
   - تحقق من التثبيت: `flutter doctor`

2. **تثبيت محرر الكود**:
   - Android Studio أو VS Code مع إضافة Flutter

3. **إعداد الجهاز**:
   - للأندرويد: قم بتفعيل وضع المطور و USB Debugging
   - للـ iOS: قم بتوصيل الجهاز وتسجيل الدخول بحساب Apple Developer
   - أو استخدم المحاكي

## خطوات التشغيل

### 1. تحميل التبعيات
```bash
flutter pub get
```

### 2. توليد ملفات JSON (إذا لزم الأمر)
```bash
flutter packages pub run build_runner build
```

### 3. تشغيل التطبيق
```bash
# للتشغيل على الجهاز المتصل
flutter run

# للتشغيل في وضع التطوير مع Hot Reload
flutter run --debug

# للتشغيل في وضع الإنتاج
flutter run --release
```

### 4. اختيار الجهاز المستهدف
```bash
# عرض الأجهزة المتاحة
flutter devices

# تشغيل على جهاز محدد
flutter run -d <device_id>
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **خطأ في تحميل التبعيات**:
   ```bash
   flutter clean
   flutter pub get
   ```

2. **مشاكل في الاتصال بالإنترنت**:
   - تأكد من اتصال الجهاز بالإنترنت
   - تحقق من إعدادات الشبكة والجدار الناري

3. **خطأ في API**:
   - تحقق من حالة CoinGecko API
   - تأكد من عدم تجاوز حد الطلبات

4. **مشاكل في التخزين المحلي**:
   ```bash
   # مسح بيانات التطبيق
   flutter clean
   ```

## الميزات المتاحة

### عند التشغيل الأول:
- سيتم إنشاء محفظة جديدة برصيد $10,000
- سيتم تحميل قائمة العملات من CoinGecko API
- ستبدأ مراقبة الأسعار تلقائياً

### الاستخدام:
1. **تصفح العملات**: في تبويب "العملات"
2. **تنفيذ صفقة**: اضغط على عملة واختر نوع الصفقة
3. **مراقبة المحفظة**: في تبويب "المحفظة"
4. **مراجعة الصفقات**: في تبويب "الصفقات"
5. **إدارة الإعدادات**: في تبويب "الإعدادات"

## ملاحظات مهمة

- **هذا تطبيق تجريبي**: لا يتم استخدام أموال حقيقية
- **الأسعار حقيقية**: يتم جلبها من CoinGecko API
- **التحديث التلقائي**: كل 30 ثانية للأسعار
- **مراقبة الصفقات**: كل 10 ثواني لوقف الخسارة/أخذ الربح

## الدعم

في حالة مواجهة مشاكل:
1. تحقق من `flutter doctor` للتأكد من إعداد البيئة
2. راجع ملف README.md للمزيد من التفاصيل
3. تحقق من وحدة التحكم للأخطاء التفصيلية
