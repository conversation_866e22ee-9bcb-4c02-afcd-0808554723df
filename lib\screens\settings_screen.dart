import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/trading_provider.dart';
import '../services/storage_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _initialBalanceController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _initialBalanceController.dispose();
    super.dispose();
  }

  void _loadSettings() {
    final initialBalance = StorageService.getSetting<double>('initial_balance', defaultValue: 10000.0);
    _initialBalanceController.text = initialBalance.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: Consumer<TradingProvider>(
        builder: (context, tradingProvider, child) {
          final portfolio = tradingProvider.portfolio;
          
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // إحصائيات المحفظة
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'إحصائيات المحفظة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildStatRow('الرصيد الابتدائي:', '\$${portfolio.initialBalance.toStringAsFixed(2)}'),
                      _buildStatRow('الرصيد الحالي:', portfolio.formattedBalance),
                      _buildStatRow('إجمالي الصفقات:', '${portfolio.totalTrades}'),
                      _buildStatRow('الصفقات الرابحة:', '${portfolio.winningTrades}'),
                      _buildStatRow('الصفقات الخاسرة:', '${portfolio.losingTrades}'),
                      _buildStatRow('معدل النجاح:', '${portfolio.winRate.toStringAsFixed(1)}%'),
                      _buildStatRow('إجمالي الربح/الخسارة:', portfolio.formattedProfitLoss),
                      _buildStatRow('نسبة الربح/الخسارة:', portfolio.formattedProfitLossPercentage),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // إعدادات المحفظة
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'إعدادات المحفظة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // إعادة تعيين المحفظة
                      ListTile(
                        leading: const Icon(Icons.refresh, color: Colors.orange),
                        title: const Text('إعادة تعيين المحفظة'),
                        subtitle: const Text('إعادة تعيين جميع الصفقات والرصيد'),
                        onTap: () => _showResetPortfolioDialog(context, tradingProvider),
                      ),
                      
                      const Divider(),
                      
                      // تصدير البيانات
                      ListTile(
                        leading: const Icon(Icons.download, color: Colors.blue),
                        title: const Text('تصدير البيانات'),
                        subtitle: const Text('تصدير بيانات المحفظة والصفقات'),
                        onTap: () => _exportData(context),
                      ),
                      
                      const Divider(),
                      
                      // مسح جميع البيانات
                      ListTile(
                        leading: const Icon(Icons.delete_forever, color: Colors.red),
                        title: const Text('مسح جميع البيانات'),
                        subtitle: const Text('حذف جميع البيانات المحفوظة'),
                        onTap: () => _showClearDataDialog(context),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // معلومات التطبيق
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معلومات التطبيق',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      ListTile(
                        leading: const Icon(Icons.info, color: Colors.blue),
                        title: const Text('حول التطبيق'),
                        subtitle: const Text('تطبيق مضاربة العملات الرقمية التجريبي'),
                        onTap: () => _showAboutDialog(context),
                      ),
                      
                      const Divider(),
                      
                      ListTile(
                        leading: const Icon(Icons.warning, color: Colors.orange),
                        title: const Text('إخلاء المسؤولية'),
                        subtitle: const Text('هذا تطبيق تجريبي للتعلم فقط'),
                        onTap: () => _showDisclaimerDialog(context),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.grey),
          ),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  void _showResetPortfolioDialog(BuildContext context, TradingProvider tradingProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين المحفظة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('هل تريد إعادة تعيين المحفظة؟ سيتم حذف جميع الصفقات وإعادة تعيين الرصيد.'),
            const SizedBox(height: 16),
            TextField(
              controller: _initialBalanceController,
              decoration: const InputDecoration(
                labelText: 'الرصيد الابتدائي الجديد',
                hintText: '10000',
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final newBalance = double.tryParse(_initialBalanceController.text) ?? 10000.0;
              tradingProvider.resetPortfolio(initialBalance: newBalance);
              StorageService.saveSetting('initial_balance', newBalance);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إعادة تعيين المحفظة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  void _exportData(BuildContext context) async {
    try {
      await StorageService.exportData();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير البيانات بنجاح (تحقق من وحدة التحكم)'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع البيانات'),
        content: const Text('هل تريد حذف جميع البيانات المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await StorageService.clearAllData();
              if (context.mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف جميع البيانات'),
                    backgroundColor: Colors.green,
                  ),
                );
                // إعادة تشغيل التطبيق أو إعادة تحميل البيانات
                context.read<TradingProvider>().resetPortfolio();
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حول التطبيق'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تطبيق مضاربة العملات الرقمية التجريبي'),
            SizedBox(height: 8),
            Text('الإصدار: 1.0.0'),
            SizedBox(height: 8),
            Text('يستخدم هذا التطبيق بيانات حقيقية من CoinGecko API لأغراض التعلم والتدريب على المضاربة.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showDisclaimerDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إخلاء المسؤولية'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تنبيه مهم:'),
            SizedBox(height: 8),
            Text('• هذا تطبيق تجريبي للتعلم فقط'),
            Text('• لا يتم استخدام أموال حقيقية'),
            Text('• الأسعار المعروضة حقيقية ولكن الصفقات وهمية'),
            Text('• لا نتحمل أي مسؤولية عن قرارات الاستثمار'),
            Text('• يرجى استشارة خبير مالي قبل الاستثمار الحقيقي'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
}
