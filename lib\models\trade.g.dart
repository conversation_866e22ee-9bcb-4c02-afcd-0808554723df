// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Trade _$TradeFromJson(Map<String, dynamic> json) => Trade(
      id: json['id'] as String,
      coinId: json['coinId'] as String,
      coinSymbol: json['coinSymbol'] as String,
      coinName: json['coinName'] as String,
      type: $enumDecode(_$TradeTypeEnumMap, json['type']),
      amount: (json['amount'] as num).toDouble(),
      entryPrice: (json['entryPrice'] as num).toDouble(),
      exitPrice: (json['exitPrice'] as num?)?.toDouble(),
      stopLoss: (json['stopLoss'] as num?)?.toDouble(),
      takeProfit: (json['takeProfit'] as num?)?.toDouble(),
      status: $enumDecode(_$TradeStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      closedAt: json['closedAt'] == null
          ? null
          : DateTime.parse(json['closedAt'] as String),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$TradeToJson(Trade instance) => <String, dynamic>{
      'id': instance.id,
      'coinId': instance.coinId,
      'coinSymbol': instance.coinSymbol,
      'coinName': instance.coinName,
      'type': _$TradeTypeEnumMap[instance.type]!,
      'amount': instance.amount,
      'entryPrice': instance.entryPrice,
      'exitPrice': instance.exitPrice,
      'stopLoss': instance.stopLoss,
      'takeProfit': instance.takeProfit,
      'status': _$TradeStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'closedAt': instance.closedAt?.toIso8601String(),
      'notes': instance.notes,
    };

const _$TradeTypeEnumMap = {
  TradeType.buy: 'buy',
  TradeType.sell: 'sell',
};

const _$TradeStatusEnumMap = {
  TradeStatus.open: 'open',
  TradeStatus.closed: 'closed',
  TradeStatus.cancelled: 'cancelled',
};
