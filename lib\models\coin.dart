import 'package:json_annotation/json_annotation.dart';

part 'coin.g.dart';

@JsonSerializable()
class Coin {
  final String id;
  final String symbol;
  final String name;
  final String image;
  @JsonKey(name: 'current_price')
  final double currentPrice;
  @Json<PERSON>ey(name: 'market_cap')
  final double? marketCap;
  @Json<PERSON>ey(name: 'market_cap_rank')
  final int? marketCapRank;
  @<PERSON>son<PERSON>ey(name: 'price_change_percentage_24h')
  final double? priceChangePercentage24h;
  @<PERSON>son<PERSON>ey(name: 'total_volume')
  final double? totalVolume;
  @<PERSON>sonKey(name: 'high_24h')
  final double? high24h;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'low_24h')
  final double? low24h;

  Coin({
    required this.id,
    required this.symbol,
    required this.name,
    required this.image,
    required this.currentPrice,
    this.marketCap,
    this.marketCapRank,
    this.priceChangePercentage24h,
    this.totalVolume,
    this.high24h,
    this.low24h,
  });

  factory Coin.fromJson(Map<String, dynamic> json) => _$CoinFromJson(json);
  Map<String, dynamic> toJson() => _$CoinToJson(this);

  bool get isPriceUp => (priceChangePercentage24h ?? 0) > 0;
  
  String get formattedPrice {
    if (currentPrice < 0.01) {
      return '\$${currentPrice.toStringAsFixed(6)}';
    } else if (currentPrice < 1) {
      return '\$${currentPrice.toStringAsFixed(4)}';
    } else {
      return '\$${currentPrice.toStringAsFixed(2)}';
    }
  }
  
  String get formattedPriceChange {
    final change = priceChangePercentage24h ?? 0;
    return '${change >= 0 ? '+' : ''}${change.toStringAsFixed(2)}%';
  }
}
