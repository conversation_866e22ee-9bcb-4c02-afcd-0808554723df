import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/coin.dart';

class CoinGeckoService {
  static const String _baseUrl = 'https://api.coingecko.com/api/v3';
  static const String _solanaCoinsUrl = '$_baseUrl/coins/markets';
  
  // قائمة بأشهر العملات الرقمية على شبكة Solana
  static const List<String> solanaMemeCoins = [
    'bonk',
    'dogwifcoin',
    'book-of-meme',
    'cat-in-a-dogs-world',
    'jeo-boden',
    'slerf',
    'wen-4',
    'myro',
    'popcat',
    'mog-coin',
    'pepe',
    'shiba-inu',
    'floki',
    'baby-doge-coin',
    'samoyedcoin',
  ];

  Future<List<Coin>> getSolanaCoins() async {
    try {
      final coinsString = solanaMemeCoins.join(',');
      final uri = Uri.parse('$_solanaCoinsUrl?vs_currency=usd&ids=$coinsString&order=market_cap_desc&per_page=50&page=1&sparkline=false&price_change_percentage=24h');
      
      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((coinData) => Coin.fromJson(coinData)).toList();
      } else {
        throw HttpException('فشل في جلب البيانات: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال بالخادم: $e');
    }
  }

  Future<Coin> getCoinById(String coinId) async {
    try {
      final uri = Uri.parse('$_baseUrl/coins/$coinId?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false');
      
      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        // تحويل البيانات إلى تنسيق متوافق مع نموذج Coin
        final coinData = {
          'id': data['id'],
          'symbol': data['symbol'],
          'name': data['name'],
          'image': data['image']['large'],
          'current_price': data['market_data']['current_price']['usd'],
          'market_cap': data['market_data']['market_cap']['usd'],
          'market_cap_rank': data['market_cap_rank'],
          'price_change_percentage_24h': data['market_data']['price_change_percentage_24h'],
          'total_volume': data['market_data']['total_volume']['usd'],
          'high_24h': data['market_data']['high_24h']['usd'],
          'low_24h': data['market_data']['low_24h']['usd'],
        };
        
        return Coin.fromJson(coinData);
      } else {
        throw HttpException('فشل في جلب بيانات العملة: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال بالخادم: $e');
    }
  }

  Future<List<double>> getCoinPriceHistory(String coinId, int days) async {
    try {
      final uri = Uri.parse('$_baseUrl/coins/$coinId/market_chart?vs_currency=usd&days=$days&interval=hourly');
      
      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> prices = data['prices'];
        
        return prices.map<double>((price) => price[1].toDouble()).toList();
      } else {
        throw HttpException('فشل في جلب تاريخ الأسعار: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال بالخادم: $e');
    }
  }
}
