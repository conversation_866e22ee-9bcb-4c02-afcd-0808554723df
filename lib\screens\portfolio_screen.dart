import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/coins_provider.dart';
import '../providers/trading_provider.dart';
import '../models/trade.dart';

class PortfolioScreen extends StatelessWidget {
  const PortfolioScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المحفظة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<CoinsProvider>().refreshCoins();
            },
          ),
        ],
      ),
      body: Consumer2<TradingProvider, CoinsProvider>(
        builder: (context, tradingProvider, coinsProvider, child) {
          final portfolio = tradingProvider.portfolio;
          final openTrades = tradingProvider.openTrades;
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات المحفظة
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'ملخص المحفظة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('الرصيد الحالي:'),
                            Text(
                              portfolio.formattedBalance,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('الربح/الخسارة الإجمالي:'),
                            Text(
                              portfolio.formattedProfitLoss,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: portfolio.totalProfitLoss >= 0 
                                    ? Colors.green 
                                    : Colors.red,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('نسبة الربح/الخسارة:'),
                            Text(
                              portfolio.formattedProfitLossPercentage,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: portfolio.totalProfitLoss >= 0 
                                    ? Colors.green 
                                    : Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // الصفقات المفتوحة
                const Text(
                  'الصفقات المفتوحة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                
                if (openTrades.isEmpty)
                  const Card(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Center(
                        child: Text('لا توجد صفقات مفتوحة'),
                      ),
                    ),
                  )
                else
                  ...openTrades.map((trade) {
                    final currentValue = tradingProvider.getCurrentTradeValue(trade);
                    final currentPL = tradingProvider.getCurrentTradeProfitLoss(trade);
                    
                    return Card(
                      child: ListTile(
                        title: Text('${trade.coinSymbol.toUpperCase()} - ${trade.type == TradeType.buy ? 'شراء' : 'بيع'}'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('الكمية: ${trade.amount.toStringAsFixed(4)}'),
                            Text('سعر الدخول: ${trade.entryPrice.toStringAsFixed(4)}\$'),
                            Text('القيمة الحالية: ${currentValue.toStringAsFixed(2)}\$'),
                          ],
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '${currentPL >= 0 ? '+' : ''}${currentPL.toStringAsFixed(2)}\$',
                              style: TextStyle(
                                color: currentPL >= 0 ? Colors.green : Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            ElevatedButton(
                              onPressed: () => _showCloseTradeDialog(context, trade),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                minimumSize: const Size(60, 30),
                              ),
                              child: const Text('إغلاق', style: TextStyle(fontSize: 12)),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showCloseTradeDialog(BuildContext context, Trade trade) {
    final coin = context.read<CoinsProvider>().findCoinById(trade.coinId);
    if (coin == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إغلاق الصفقة'),
        content: Text('هل تريد إغلاق صفقة ${trade.coinSymbol.toUpperCase()} بالسعر الحالي ${coin.formattedPrice}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<TradingProvider>().closeTrade(trade.id, coin.currentPrice);
              Navigator.pop(context);
            },
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
