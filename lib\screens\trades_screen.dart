import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/trading_provider.dart';
import '../models/trade.dart';

class TradesScreen extends StatefulWidget {
  const TradesScreen({super.key});

  @override
  State<TradesScreen> createState() => _TradesScreenState();
}

class _TradesScreenState extends State<TradesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الصفقات'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الكل'),
            Tab(text: 'المفتوحة'),
            Tab(text: 'المغلقة'),
          ],
        ),
      ),
      body: Consumer<TradingProvider>(
        builder: (context, tradingProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              // جميع الصفقات
              TradesList(trades: tradingProvider.portfolio.trades),
              // الصفقات المفتوحة
              TradesList(trades: tradingProvider.openTrades),
              // الصفقات المغلقة
              TradesList(trades: tradingProvider.closedTrades),
            ],
          );
        },
      ),
    );
  }
}

class TradesList extends StatelessWidget {
  final List<Trade> trades;

  const TradesList({super.key, required this.trades});

  @override
  Widget build(BuildContext context) {
    if (trades.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد صفقات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: trades.length,
      itemBuilder: (context, index) {
        final trade = trades[index];
        return TradeCard(trade: trade);
      },
    );
  }
}

class TradeCard extends StatelessWidget {
  final Trade trade;

  const TradeCard({super.key, required this.trade});

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: trade.type == TradeType.buy ? Colors.green : Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        trade.type == TradeType.buy ? 'شراء' : 'بيع',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      trade.coinSymbol.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(trade.status),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(trade.status),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // تفاصيل الصفقة
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailRow('الكمية:', '${trade.amount.toStringAsFixed(4)}'),
                      _buildDetailRow('سعر الدخول:', '\$${trade.entryPrice.toStringAsFixed(4)}'),
                      if (trade.exitPrice != null)
                        _buildDetailRow('سعر الخروج:', '\$${trade.exitPrice!.toStringAsFixed(4)}'),
                      _buildDetailRow('القيمة الإجمالية:', '\$${trade.totalValue.toStringAsFixed(2)}'),
                    ],
                  ),
                ),
                if (trade.status == TradeStatus.closed)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        trade.formattedProfitLoss,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: trade.isProfit ? Colors.green : Colors.red,
                        ),
                      ),
                      Text(
                        trade.formattedProfitLossPercentage,
                        style: TextStyle(
                          fontSize: 14,
                          color: trade.isProfit ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
            
            // وقف الخسارة وأخذ الربح
            if (trade.stopLoss != null || trade.takeProfit != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  if (trade.stopLoss != null)
                    Expanded(
                      child: _buildDetailRow('وقف الخسارة:', '\$${trade.stopLoss!.toStringAsFixed(4)}'),
                    ),
                  if (trade.takeProfit != null)
                    Expanded(
                      child: _buildDetailRow('أخذ الربح:', '\$${trade.takeProfit!.toStringAsFixed(4)}'),
                    ),
                ],
              ),
            ],
            
            // التواريخ
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'تاريخ الفتح: ${dateFormat.format(trade.createdAt)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                if (trade.closedAt != null)
                  Text(
                    'تاريخ الإغلاق: ${dateFormat.format(trade.closedAt!)}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
              ],
            ),
            
            // الملاحظات
            if (trade.notes != null && trade.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'ملاحظات: ${trade.notes}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
            
            // أزرار الإجراءات للصفقات المفتوحة
            if (trade.status == TradeStatus.open) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _showCloseTradeDialog(context, trade),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                      ),
                      child: const Text('إغلاق'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _showCancelTradeDialog(context, trade),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(TradeStatus status) {
    switch (status) {
      case TradeStatus.open:
        return Colors.blue;
      case TradeStatus.closed:
        return Colors.green;
      case TradeStatus.cancelled:
        return Colors.grey;
    }
  }

  String _getStatusText(TradeStatus status) {
    switch (status) {
      case TradeStatus.open:
        return 'مفتوحة';
      case TradeStatus.closed:
        return 'مغلقة';
      case TradeStatus.cancelled:
        return 'ملغاة';
    }
  }

  void _showCloseTradeDialog(BuildContext context, Trade trade) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إغلاق الصفقة'),
        content: Text('هل تريد إغلاق صفقة ${trade.coinSymbol.toUpperCase()}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // يجب الحصول على السعر الحالي من CoinsProvider
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('يرجى إغلاق الصفقة من شاشة المحفظة للحصول على السعر الحالي'),
                ),
              );
            },
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showCancelTradeDialog(BuildContext context, Trade trade) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الصفقة'),
        content: Text('هل تريد إلغاء صفقة ${trade.coinSymbol.toUpperCase()}؟ سيتم إرجاع المبلغ إلى رصيدك.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<TradingProvider>().cancelTrade(trade.id);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إلغاء الصفقة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('نعم، إلغاء'),
          ),
        ],
      ),
    );
  }
}
