# دليل تثبيت Flutter وتشغيل التطبيق

## 1. تثبيت Flutter SDK

### على Windows:
1. قم بتحميل Flutter SDK من: https://docs.flutter.dev/get-started/install/windows
2. استخرج الملف المضغوط إلى مجلد (مثل `C:\flutter`)
3. أضف `C:\flutter\bin` إلى متغير البيئة PATH
4. أعد تشغيل Command Prompt أو PowerShell

### على macOS:
1. قم بتحميل Flutter SDK من: https://docs.flutter.dev/get-started/install/macos
2. استخرج الملف إلى مجلد (مثل `~/flutter`)
3. أضف المسار إلى `.bashrc` أو `.zshrc`:
   ```bash
   export PATH="$PATH:~/flutter/bin"
   ```

### على Linux:
1. قم بتحميل Flutter SDK من: https://docs.flutter.dev/get-started/install/linux
2. استخرج الملف إلى مجلد (مثل `~/flutter`)
3. أض<PERSON> المسار إلى `.bashrc`:
   ```bash
   export PATH="$PATH:~/flutter/bin"
   ```

## 2. التحقق من التثبيت

```bash
flutter doctor
```

يجب أن ترى رسالة تؤكد تثبيت Flutter بنجاح.

## 3. تثبيت Android Studio (للأندرويد)

1. قم بتحميل Android Studio من: https://developer.android.com/studio
2. ثبت Android SDK
3. قم بتفعيل وضع المطور على جهاز الأندرويد
4. فعل USB Debugging

## 4. إعداد iOS (للـ iPhone/iPad)

1. ثبت Xcode من App Store (macOS فقط)
2. قم بتشغيل: `sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer`
3. وافق على رخصة Xcode: `sudo xcodebuild -license`

## 5. تشغيل التطبيق

### تحميل التبعيات:
```bash
cd "demo trading meme coins"
flutter pub get
```

### تشغيل التطبيق:
```bash
# للتشغيل على الجهاز المتصل
flutter run

# أو للتشغيل في المتصفح (Web)
flutter run -d chrome
```

## 6. استكشاف الأخطاء

### إذا واجهت مشاكل:

1. **تحقق من حالة Flutter**:
   ```bash
   flutter doctor -v
   ```

2. **مسح الكاش**:
   ```bash
   flutter clean
   flutter pub get
   ```

3. **تحديث Flutter**:
   ```bash
   flutter upgrade
   ```

4. **مشاكل في الشبكة**:
   - تأكد من الاتصال بالإنترنت
   - قد تحتاج لاستخدام VPN في بعض البلدان

## 7. متطلبات النظام

### الحد الأدنى:
- **Windows**: Windows 10 أو أحدث
- **macOS**: macOS 10.14 أو أحدث  
- **Linux**: Ubuntu 18.04 أو أحدث
- **RAM**: 4 GB (8 GB مستحسن)
- **مساحة القرص**: 2.8 GB للـ SDK

### للتطوير:
- **Android Studio** أو **VS Code** مع إضافة Flutter
- **Git** لإدارة الإصدارات
- **Chrome** للتشغيل على الويب

## 8. الأجهزة المدعومة

- **Android**: API level 16 (Android 4.1) أو أحدث
- **iOS**: iOS 11.0 أو أحدث
- **Web**: Chrome, Firefox, Safari, Edge
- **Desktop**: Windows, macOS, Linux

## 9. نصائح للمطورين

1. **استخدم Hot Reload**: اضغط `r` في Terminal أثناء التشغيل
2. **استخدم Hot Restart**: اضغط `R` في Terminal
3. **فحص الأداء**: استخدم Flutter Inspector في IDE
4. **تصحيح الأخطاء**: استخدم Debugger في VS Code أو Android Studio

## 10. موارد إضافية

- **الوثائق الرسمية**: https://docs.flutter.dev/
- **أمثلة الكود**: https://github.com/flutter/samples
- **مجتمع Flutter**: https://flutter.dev/community
- **دروس تعليمية**: https://flutter.dev/learn

---

بعد اتباع هذه الخطوات، ستكون قادراً على تشغيل تطبيق مضاربة العملات الرقمية بنجاح!
