name: solana_meme_trader
description: تطبيق مضاربة العملات الرقمية على شبكة Solana للـ memecoins

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.2
  flutter_localizations:
    sdk: flutter

  # State Management
  provider: ^6.1.1

  # HTTP Requests
  http: ^1.1.0

  # Local Storage
  shared_preferences: ^2.2.2

  # Charts
  fl_chart: ^0.66.0

  # Date/Number Formatting
  intl: ^0.19.0

  # JSON Handling
  json_annotation: ^4.8.1

  # UUID Generation
  uuid: ^4.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  json_serializable: ^6.7.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true

  # Assets
  # assets:
  #   - assets/images/
  #   - assets/icons/

  # Fonts
  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
